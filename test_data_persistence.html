<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据持久化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .item {
            padding: 10px;
            margin: 5px 0;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .pagination {
            margin: 20px 0;
            text-align: center;
        }
        .pagination button {
            margin: 0 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .pagination button:hover {
            background: #0056b3;
        }
        .pagination button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .current-page {
            font-weight: bold;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <h1>数据持久化测试页面</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>这个页面用于测试爬虫的数据持久化功能：</p>
        <ul>
            <li>页面有多页数据，每页10个项目</li>
            <li>可以测试自动翻页和数据累积</li>
            <li>刷新页面后数据应该能够恢复</li>
            <li>支持继续爬取和重新开始</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>当前页面：<span class="current-page" id="currentPageDisplay">1</span></h2>
        
        <div id="itemList">
            <!-- 动态生成的列表项 -->
        </div>
        
        <div class="pagination">
            <button id="prevBtn" onclick="goToPage(currentPage - 1)">上一页</button>
            <span>第 <span id="pageNumber">1</span> 页，共 5 页</span>
            <button id="nextBtn" onclick="goToPage(currentPage + 1)">下一页</button>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const totalPages = 5;
        const itemsPerPage = 10;

        function generateItems(page) {
            const items = [];
            const startIndex = (page - 1) * itemsPerPage;
            
            for (let i = 1; i <= itemsPerPage; i++) {
                const itemIndex = startIndex + i;
                items.push({
                    title: `测试项目 ${itemIndex}`,
                    author: `作者${itemIndex}`,
                    time: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleDateString(),
                    replies: Math.floor(Math.random() * 100),
                    link: `#item-${itemIndex}`
                });
            }
            
            return items;
        }

        function renderItems(items) {
            const listContainer = document.getElementById('itemList');
            listContainer.innerHTML = '';
            
            items.forEach((item, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'item';
                itemDiv.innerHTML = `
                    <h3><a href="${item.link}">${item.title}</a></h3>
                    <p>作者: ${item.author} | 时间: ${item.time} | 回复: ${item.replies}</p>
                `;
                listContainer.appendChild(itemDiv);
            });
        }

        function updatePagination() {
            document.getElementById('pageNumber').textContent = currentPage;
            document.getElementById('currentPageDisplay').textContent = currentPage;
            
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }

        function goToPage(page) {
            if (page < 1 || page > totalPages) return;
            
            currentPage = page;
            const items = generateItems(currentPage);
            renderItems(items);
            updatePagination();
            
            // 更新URL以模拟真实的分页
            const url = new URL(window.location);
            url.searchParams.set('page', currentPage);
            window.history.pushState({}, '', url);
        }

        // 初始化页面
        function init() {
            // 从URL参数获取当前页
            const urlParams = new URLSearchParams(window.location.search);
            const pageParam = urlParams.get('page');
            if (pageParam) {
                currentPage = parseInt(pageParam);
            }
            
            goToPage(currentPage);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
        
        // 监听浏览器前进后退
        window.addEventListener('popstate', function(event) {
            const urlParams = new URLSearchParams(window.location.search);
            const pageParam = urlParams.get('page');
            if (pageParam) {
                currentPage = parseInt(pageParam);
                goToPage(currentPage);
            }
        });
    </script>
</body>
</html>
