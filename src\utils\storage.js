import { GM_getValue, GM_setValue } from '$';

/**
 * 存储工具类 - 处理数据的保存和加载
 */
export class StorageManager {
  constructor() {
    this.storagePrefix = 'crawler_';
  }

  /**
   * 保存爬取的数据
   */
  saveData(key, data) {
    try {
      const storageKey = this.storagePrefix + key;
      const dataToSave = {
        data,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };

      if (typeof GM_setValue !== 'undefined') {
        console.log('GM_getValue');
        GM_setValue(storageKey, JSON.stringify(dataToSave));
      } else {
        console.log('2222222222222');
        localStorage.setItem(storageKey, JSON.stringify(dataToSave));
      }

      return true;
    } catch (error) {
      console.error('保存数据失败:', error);
      return false;
    }
  }

  /**
   * 加载数据
   */
  loadData(key) {
    try {
      const storageKey = this.storagePrefix + key;
      let dataStr;

      if (typeof GM_getValue !== 'undefined') {
        dataStr = GM_getValue(storageKey, null);
      } else {
        dataStr = localStorage.getItem(storageKey);
      }

      if (!dataStr) return null;

      const parsed = JSON.parse(dataStr);
      return parsed.data;
    } catch (error) {
      console.error('加载数据失败:', error);
      return null;
    }
  }

  /**
   * 删除数据
   */
  deleteData(key) {
    try {
      const storageKey = this.storagePrefix + key;

      if (typeof GM_deleteValue !== 'undefined') {
        GM_deleteValue(storageKey);
      } else {
        localStorage.removeItem(storageKey);
      }

      return true;
    } catch (error) {
      console.error('删除数据失败:', error);
      return false;
    }
  }

  /**
   * 获取所有保存的数据键
   */
  getAllKeys() {
    try {
      const keys = [];

      if (typeof GM_listValues !== 'undefined') {
        const allKeys = GM_listValues();
        keys.push(...allKeys.filter(key => key.startsWith(this.storagePrefix)));
      } else {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith(this.storagePrefix)) {
            keys.push(key);
          }
        }
      }

      return keys.map(key => key.replace(this.storagePrefix, ''));
    } catch (error) {
      console.error('获取数据键失败:', error);
      return [];
    }
  }

  /**
   * 导出数据到文件
   */
  exportToFile(data, filename, format = 'json') {
    try {
      let content;
      let mimeType;

      switch (format.toLowerCase()) {
        case 'json':
          content = JSON.stringify(data, null, 2);
          mimeType = 'application/json';
          filename = filename.endsWith('.json') ? filename : filename + '.json';
          break;
        case 'csv':
          content = this.convertToCSV(data);
          mimeType = 'text/csv';
          filename = filename.endsWith('.csv') ? filename : filename + '.csv';
          break;
        default:
          throw new Error(`不支持的格式: ${format}`);
      }

      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return true;
    } catch (error) {
      console.error('导出文件失败:', error);
      return false;
    }
  }

  /**
   * 转换为CSV格式
   */
  convertToCSV(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return '';
    }

    // 获取所有可能的列名
    const allKeys = new Set();
    data.forEach(item => {
      Object.keys(item).forEach(key => {
        if (typeof item[key] !== 'object' || item[key] === null) {
          allKeys.add(key);
        }
      });
    });

    const headers = Array.from(allKeys);
    const csvRows = [headers.join(',')];

    data.forEach(item => {
      const row = headers.map(header => {
        let value = item[header];
        
        if (value === null || value === undefined) {
          return '';
        }
        
        if (typeof value === 'string') {
          // 转义双引号并用双引号包围
          value = `"${value.replace(/"/g, '""')}"`;
        }
        
        return value;
      });
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  /**
   * 从文件导入数据
   */
  importFromFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const content = e.target.result;
          let data;

          if (file.name.endsWith('.json')) {
            data = JSON.parse(content);
          } else if (file.name.endsWith('.csv')) {
            data = this.parseCSV(content);
          } else {
            throw new Error('不支持的文件格式');
          }

          resolve(data);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file);
    });
  }

  /**
   * 解析CSV内容
   */
  parseCSV(content) {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/^"|"$/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i]);
      if (values.length === headers.length) {
        const item = {};
        headers.forEach((header, index) => {
          item[header] = values[index];
        });
        data.push(item);
      }
    }

    return data;
  }

  /**
   * 解析CSV行
   */
  parseCSVLine(line) {
    const values = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"';
          i++; // 跳过下一个引号
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    values.push(current.trim());
    return values;
  }

  /**
   * 清理过期数据
   */
  cleanupOldData(daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const keys = this.getAllKeys();
      let cleanedCount = 0;

      keys.forEach(key => {
        try {
          const storageKey = this.storagePrefix + key;
          let dataStr;

          if (typeof GM_getValue !== 'undefined') {
            dataStr = GM_getValue(storageKey, null);
    
          } else {
          
            dataStr = localStorage.getItem(storageKey);
          }

          if (dataStr) {
            const parsed = JSON.parse(dataStr);
            const dataDate = new Date(parsed.timestamp);
            
            if (dataDate < cutoffDate) {
              this.deleteData(key);
              cleanedCount++;
            }
          }
        } catch (error) {
          console.warn(`清理数据 ${key} 时出错:`, error);
        }
      });

      return cleanedCount;
    } catch (error) {
      console.error('清理过期数据失败:', error);
      return 0;
    }
  }

  /**
   * 获取存储使用情况
   */
  getStorageInfo() {
    try {
      const keys = this.getAllKeys();
      let totalSize = 0;
      const items = [];

      keys.forEach(key => {
        try {
          const storageKey = this.storagePrefix + key;
          let dataStr;

          if (typeof GM_getValue !== 'undefined') {
            
            
            dataStr = GM_getValue(storageKey, '{}');
          } else {
          
            dataStr = localStorage.getItem(storageKey) || '{}';
          }

          const size = new Blob([dataStr]).size;
          totalSize += size;

          const parsed = JSON.parse(dataStr);
          items.push({
            key,
            size,
            timestamp: parsed.timestamp,
            version: parsed.version
          });
        } catch (error) {
          console.warn(`获取 ${key} 信息时出错:`, error);
        }
      });

      return {
        totalItems: keys.length,
        totalSize,
        items: items.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      };
    } catch (error) {
      console.error('获取存储信息失败:', error);
      return { totalItems: 0, totalSize: 0, items: [] };
    }
  }
}

// 创建全局实例
export const storage = new StorageManager();
