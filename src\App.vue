<script setup>
import { onMounted, ref } from "vue";
import CrawlerUI from "./components/CrawlerUI.vue";

const showCrawler = ref(false);

onMounted(() => {
  console.log("Data Crawler mounted");

  // 检查是否在支持的页面上
  if (document.querySelector('.cell.item, .item, li, .list-item, .post, .article')) {
    // showCrawler.value = true;
  }

  // 创建切换按钮
  createToggleButton();
});

const createToggleButton = () => {
  // 创建浮动按钮
  const button = document.createElement('div');
  button.id = 'crawler-toggle-btn';
  button.innerHTML = '🕷️';
  button.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: #409eff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10000;
    font-size: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
  `;

  button.addEventListener('mouseenter', () => {
    button.style.transform = 'scale(1.1)';
    button.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.25)';
  });

  button.addEventListener('mouseleave', () => {
    button.style.transform = 'scale(1)';
    button.style.boxShadow = '0 2px 12px rgba(0, 0, 0, 0.15)';
  });

  button.addEventListener('click', () => {
    showCrawler.value = !showCrawler.value;
  });

  document.body.appendChild(button);
};
</script>

<template>
  <div v-if="showCrawler" class="crawler-app">
    <!-- 遮罩层 -->
    <div class="crawler-overlay" @click="showCrawler = false"></div>

    <!-- 爬虫界面 -->
    <div class="crawler-container">
      <div class="crawler-header">
        <h2>Crawler</h2>
        <button class="close-btn" @click="showCrawler = false">×</button>
      </div>

      <div class="crawler-content">
        <CrawlerUI />
      </div>
    </div>
  </div>
</template>

<style scoped>
.crawler-app {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.crawler-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.crawler-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 1200px;
  height: 90vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.crawler-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.crawler-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.crawler-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* 滚动条样式 */
.crawler-content::-webkit-scrollbar {
  width: 8px;
}

.crawler-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.crawler-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.crawler-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .crawler-container {
    width: 95vw;
    height: 95vh;
  }

  .crawler-header {
    padding: 16px 20px;
  }

  .crawler-header h2 {
    font-size: 18px;
  }
}

/* 动画效果 */
.crawler-app {
  animation: fadeIn 0.3s ease-out;
}

.crawler-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}
</style>
