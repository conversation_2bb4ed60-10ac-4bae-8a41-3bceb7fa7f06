import { createApp } from 'vue';
import './style.css';
import App from './App.vue';

// 等待页面加载完成
const initApp = () => {
  // 创建应用容器
  const appContainer = document.createElement('div');
  appContainer.id = 'data-crawler-app';
  appContainer.style.cssText = `
    position: relative;
    z-index: 9999;
  `;

  // 添加到页面
  document.body.appendChild(appContainer);

  // 创建Vue应用
  const app = createApp(App);
  app.mount(appContainer);

  console.log('Data Crawler 已启动');
};

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}
