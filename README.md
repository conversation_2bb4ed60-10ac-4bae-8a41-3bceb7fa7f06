# 通用网页数据爬虫

一个基于Vue 3和用户脚本技术的通用网页列表数据爬虫，能够自动识别页面结构、提取列表数据并支持自动翻页。

## 功能特点

### 🎯 智能识别
- **自动检测列表结构** - 智能分析页面DOM结构，自动识别列表容器
- **分页自动检测** - 自动识别"下一页"按钮和页码链接
- **多种选择器支持** - 支持常见的列表选择器模式

### 📊 数据提取
- **结构化数据提取** - 自动提取标题、链接、作者、时间、回复数等信息
- **图片和链接收集** - 自动收集页面中的图片和相关链接
- **数据验证** - 内置数据验证机制，确保提取质量

### 🔄 自动翻页
- **智能翻页** - 自动点击下一页并继续爬取
- **循环检测** - 防止无限循环，智能停止
- **错误重试** - 支持网络错误重试机制

### 💾 数据管理
- **多格式导出** - 支持JSON、CSV格式导出
- **本地存储** - 自动保存爬取进度和配置
- **数据统计** - 实时显示爬取统计信息

### 🎨 用户界面
- **现代化UI** - 基于Element Plus的美观界面
- **实时监控** - 实时显示爬取进度和状态
- **配置管理** - 可视化配置管理界面

## 安装和使用

### 1. 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd data-crawler

# 安装依赖
npm install

# 开发模式
npm run dev

# 构建用户脚本
npm run build
```

### 2. 用户脚本安装

1. 安装用户脚本管理器（如Tampermonkey、Greasemonkey等）
2. 运行 `npm run build` 构建用户脚本
3. 在 `dist/` 目录找到生成的 `.user.js` 文件
4. 在用户脚本管理器中安装该脚本

### 3. 使用方法

1. **访问目标网站** - 打开包含列表数据的网页
2. **点击爬虫按钮** - 页面右上角会出现🕷️按钮
3. **自动检测** - 点击"自动检测"按钮分析页面结构
4. **预览数据** - 点击"预览当前页"查看提取效果
5. **开始爬取** - 配置参数后点击"开始爬取"
6. **导出数据** - 爬取完成后可导出为JSON或CSV格式

## 配置说明

### 基本设置
- **最大页数** - 限制爬取的最大页面数量
- **页面延迟** - 翻页之间的等待时间（毫秒）
- **重试次数** - 网络错误时的重试次数

### 高级配置
爬虫支持自定义选择器配置，可以通过修改配置文件适配不同网站：

```javascript
// 列表项选择器
listSelectors: [
  '.cell.item',     // V2EX样式
  '.item',          // 通用item类
  'li',             // 列表项
  '.list-item',     // 通用列表项
  // ... 更多选择器
]

// 分页选择器
paginationSelectors: {
  nextButton: [
    'a[title="下一页"]',
    '.next',
    '.page-next',
    // ... 更多选择器
  ]
}
```

## 支持的网站类型

### 已测试网站
- ✅ V2EX论坛
- ✅ 新闻列表页面
- ✅ 博客文章列表
- ✅ 电商产品列表
- ✅ 社交媒体动态

### 适用场景
- 论坛帖子列表
- 新闻文章列表
- 博客文章归档
- 产品目录页面
- 搜索结果页面
- 任何具有列表结构的页面

## 技术架构

### 核心组件
- **CrawlerCore** - 爬虫核心控制器
- **PageParser** - 页面结构解析器
- **DataExtractor** - 数据提取器
- **PaginationHandler** - 分页处理器
- **ConfigManager** - 配置管理器

### 技术栈
- **Vue 3** - 前端框架
- **Element Plus** - UI组件库
- **Vite** - 构建工具
- **vite-plugin-monkey** - 用户脚本插件
