import { ConfigManager } from './ConfigManager.js';
import { PageParser } from './PageParser.js';
import { DataExtractor } from './DataExtractor.js';
import { PaginationHandler } from './PaginationHandler.js';
import { storage } from '../utils/storage.js';

/**
 * 爬虫核心类 - 协调各个组件完成爬取任务
 */
export class CrawlerCore {
  constructor() {
    this.configManager = new ConfigManager();
    this.config = this.configManager.getConfig();

    this.pageParser = new PageParser(this.config);
    this.dataExtractor = new DataExtractor(this.config);
    this.paginationHandler = new PaginationHandler(this.config);

    this.isRunning = false;
    this.isPaused = false;
    this.currentPageData = [];
    this.allData = [];
    this.crawlStats = {
      startTime: null,
      endTime: null,
      totalPages: 0,
      totalItems: 0,
      errors: []
    };

    this.eventListeners = new Map();
    this.sessionKey = this.generateSessionKey();

    // 尝试恢复之前的数据
  
    
    this.loadPreviousData();
  }

  /**
   * 生成会话键
   */
  generateSessionKey() {
    const url = window.location.href;
    const urlHash = this.hashCode(url);
    return `session_${urlHash}`;
  }

  /**
   * 简单的字符串哈希函数
   */
  hashCode(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 加载之前的数据
   */
  loadPreviousData() {
    try {
      console.log('11');
      
      const savedData = storage.loadData(this.sessionKey);
      if (savedData && savedData.allData) {
        this.allData = savedData.allData;
        this.crawlStats = { ...this.crawlStats, ...savedData.crawlStats };
        this.emit('dataRestored', {
          itemCount: this.allData.length,
          pages: this.crawlStats.totalPages
        });
      }
    } catch (error) {
      console.warn('加载之前的数据失败:', error);
    }
  }

  /**
   * 保存当前数据
   */
  saveCurrentData() {
    try {
      const dataToSave = {
        allData: this.allData,
        crawlStats: this.crawlStats,
        timestamp: new Date().toISOString(),
        url: window.location.href
      };
      storage.saveData(this.sessionKey, dataToSave);
    } catch (error) {
      console.warn('保存数据失败:', error);
    }
  }

  /**
   * 开始爬取
   */
  async startCrawling(options = {}) {
    if (this.isRunning) {
      throw new Error('爬虫正在运行中');
    }

    this.isRunning = true;
    this.isPaused = false;

    // 如果是新的爬取任务，重置数据；否则继续之前的进度
    const isNewCrawl = options.resetData !== false;
    if (isNewCrawl) {
      this.allData = [];
      this.crawlStats = {
        startTime: new Date(),
        endTime: null,
        totalPages: 0,
        totalItems: 0,
        errors: []
      };
    } else {
      // 继续之前的爬取，只更新开始时间
      this.crawlStats.startTime = this.crawlStats.startTime || new Date();
    }

    // 合并配置
    const crawlConfig = { ...this.config.crawlSettings, ...options };

    this.emit('crawlStart', {
      config: crawlConfig,
      isResume: !isNewCrawl,
      existingItems: this.allData.length
    });

    try {
      await this.crawlAllPages(crawlConfig);
      this.crawlStats.endTime = new Date();

      // 保存最终数据
      this.saveCurrentData();

      this.emit('crawlComplete', {
        data: this.allData,
        stats: this.crawlStats
      });
    } catch (error) {
      this.crawlStats.errors.push({
        message: error.message,
        timestamp: new Date(),
        page: this.crawlStats.totalPages + 1
      });

      // 保存错误状态的数据
      this.saveCurrentData();

      this.emit('crawlError', { error, stats: this.crawlStats });
      throw error;
    } finally {
      this.isRunning = false;
    }

    return {
      data: this.allData,
      stats: this.crawlStats
    };
  }

  /**
   * 爬取所有页面
   */
  async crawlAllPages(config) {
    let pageCount = 0;
    const maxPages = config.maxPages || 10;

    while (pageCount < maxPages && !this.isPaused) {
      try {
        // 检查是否可以继续
        if (pageCount > 0 && !this.paginationHandler.canContinue(pageCount, maxPages)) {
          this.emit('crawlInfo', { message: '已到达最后一页或达到最大页数限制' });
          break;
        }

        this.emit('pageStart', { 
          pageNumber: pageCount + 1, 
          url: window.location.href 
        });

        // 爬取当前页面
        const pageData = await this.crawlCurrentPage();
        
        if (pageData.items.length > 0) {
          this.allData.push(...pageData.items);
          this.crawlStats.totalItems += pageData.items.length;
        }

        pageCount++;
        this.crawlStats.totalPages = pageCount;

        this.emit('pageComplete', {
          pageNumber: pageCount,
          itemCount: pageData.items.length,
          totalItems: this.crawlStats.totalItems,
          parseResult: pageData.parseResult
        });

        // 实时保存数据
        this.saveCurrentData();

        // 如果不是最后一页，导航到下一页
        if (pageCount < maxPages && this.paginationHandler.canContinue(pageCount, maxPages)) {
          this.emit('pageNavigation', {
            from: pageCount,
            to: pageCount + 1
          });

          await this.paginationHandler.goToNextPage();

          // 等待页面加载完成
          await this.waitForPageLoad();
        }

      } catch (error) {
        this.crawlStats.errors.push({
          message: error.message,
          timestamp: new Date(),
          page: pageCount + 1,
          url: window.location.href
        });

        this.emit('pageError', {
          pageNumber: pageCount + 1,
          error: error.message,
          url: window.location.href
        });

        // 根据错误类型决定是否继续
        if (this.shouldStopOnError(error)) {
          throw error;
        }

        // 尝试继续下一页
        pageCount++;
      }
    }
  }

  /**
   * 爬取当前页面
   */
  async crawlCurrentPage() {
    // 解析页面结构
    const parseResult = this.pageParser.parsePageItems();
    
    if (!parseResult.items || parseResult.items.length === 0) {
      throw new Error('当前页面未找到任何列表项');
    }

    // 验证解析结果
    const validation = this.pageParser.validateParseResult(parseResult);
    if (!validation.isValid) {
      this.emit('parseWarning', {
        issues: validation.issues,
        confidence: validation.confidence
      });
    }

    // 提取数据
    const extractedData = this.dataExtractor.extractFromItems(parseResult.items);
    
    // 获取提取统计
    const extractionStats = this.dataExtractor.getExtractionStats(extractedData);

    this.currentPageData = extractedData;

    return {
      items: extractedData,
      parseResult,
      extractionStats,
      validation
    };
  }

  /**
   * 暂停爬取
   */
  pause() {
    this.isPaused = true;
    this.emit('crawlPaused');
  }

  /**
   * 恢复爬取
   */
  resume() {
    this.isPaused = false;
    this.emit('crawlResumed');
  }

  /**
   * 停止爬取
   */
  stop() {
    this.isRunning = false;
    this.isPaused = false;

    // 保存当前状态
    this.saveCurrentData();

    this.emit('crawlStopped');
  }

  /**
   * 清除所有数据
   */
  clearAllData() {
    this.allData = [];
    this.crawlStats = {
      startTime: null,
      endTime: null,
      totalPages: 0,
      totalItems: 0,
      errors: []
    };

    // 删除存储的数据
    storage.deleteData(this.sessionKey);

    this.emit('dataCleared');
  }

  /**
   * 获取当前所有数据
   */
  getAllData() {
    return {
      data: this.allData,
      stats: this.crawlStats,
      sessionKey: this.sessionKey
    };
  }

  /**
   * 继续之前的爬取
   */
  async resumeCrawling(options = {}) {
    return this.startCrawling({ ...options, resetData: false });
  }

  /**
   * 获取当前页面预览
   */
  async previewCurrentPage() {
    try {
      const pageData = await this.crawlCurrentPage();
      return {
        success: true,
        data: pageData,
        pagination: this.paginationHandler.detectPagination()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 自动检测页面配置
   */
  autoDetectConfig() {
    const detectedConfig = this.configManager.autoDetectConfig();
    this.emit('configDetected', detectedConfig);
    return detectedConfig;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.configManager.saveConfig(this.config);
    
    // 更新各组件的配置
    this.pageParser.config = this.config;
    this.dataExtractor.config = this.config;
    this.paginationHandler.config = this.config;
    
    this.emit('configUpdated', this.config);
  }

  /**
   * 等待页面加载完成
   */
  async waitForPageLoad() {
    return new Promise((resolve) => {
      const checkLoad = () => {
        if (document.readyState === 'complete') {
          // 额外等待一段时间确保动态内容加载
          setTimeout(resolve, this.config.crawlSettings.delay || 2000);
        } else {
          setTimeout(checkLoad, 100);
        }
      };
      checkLoad();
    });
  }

  /**
   * 判断是否应该在错误时停止
   */
  shouldStopOnError(error) {
    const fatalErrors = [
      '检测到循环',
      '无法获取下一页URL',
      '页面加载超时'
    ];
    
    return fatalErrors.some(fatalError => error.message.includes(fatalError));
  }

  /**
   * 获取爬取统计信息
   */
  getStats() {
    return {
      ...this.crawlStats,
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      currentPageItems: this.currentPageData.length,
      pagination: this.paginationHandler.getPaginationStats()
    };
  }

  /**
   * 导出数据
   */
  exportData(format = 'json') {
    const data = {
      crawlInfo: {
        url: window.location.origin,
        startTime: this.crawlStats.startTime,
        endTime: this.crawlStats.endTime,
        totalPages: this.crawlStats.totalPages,
        totalItems: this.crawlStats.totalItems
      },
      items: this.allData
    };

    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(data, null, 2);
      case 'csv':
        return this.convertToCSV(this.allData);
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  }

  /**
   * 转换为CSV格式
   */
  convertToCSV(data) {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]).filter(key => 
      typeof data[0][key] !== 'object' || data[0][key] === null
    );
    
    const csvRows = [headers.join(',')];
    
    data.forEach(item => {
      const row = headers.map(header => {
        const value = item[header];
        return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
      });
      csvRows.push(row.join(','));
    });
    
    return csvRows.join('\n');
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件监听器执行失败 (${event}):`, error);
        }
      });
    }
  }
}
