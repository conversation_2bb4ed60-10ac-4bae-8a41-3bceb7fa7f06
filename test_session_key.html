<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Key 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .url-test {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .result {
            font-family: monospace;
            background: #f0f0f0;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .current-url {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Session Key 生成测试</h1>
    
    <div class="test-section">
        <h2>当前页面信息</h2>
        <div class="current-url">
            <strong>当前URL:</strong> <span id="currentUrl"></span><br>
            <strong>基础URL:</strong> <span id="baseUrl"></span><br>
            <strong>Session Key:</strong> <span id="sessionKey"></span>
        </div>
        
        <button onclick="updateCurrentInfo()">刷新信息</button>
        <button onclick="testCrawlerCore()">测试 CrawlerCore</button>
    </div>

    <div class="test-section">
        <h2>URL 测试案例</h2>
        <p>测试不同的分页URL是否生成相同的Session Key：</p>
        
        <div id="testResults"></div>
        
        <button onclick="runTests()">运行测试</button>
    </div>

    <div class="test-section">
        <h2>模拟分页导航</h2>
        <p>点击下面的链接模拟分页，观察Session Key是否保持一致：</p>
        
        <button onclick="goToPage(1)">第1页</button>
        <button onclick="goToPage(2)">第2页</button>
        <button onclick="goToPage(3)">第3页</button>
        <button onclick="goToPage(10)">第10页</button>
        
        <br><br>
        
        <button onclick="goToPageWithPath(1)">路径分页 /page/1</button>
        <button onclick="goToPageWithPath(2)">路径分页 /page/2</button>
        <button onclick="goToPageWithPath(3)">路径分页 /page/3</button>
    </div>

    <script type="module">
        // 导入 CrawlerCore 用于测试
        import { CrawlerCore } from './src/crawler/CrawlerCore.js';
        
        // 全局变量
        window.testCrawlerCore = function() {
            try {
                const crawler = new CrawlerCore();
                console.log('CrawlerCore 测试:', {
                    sessionKey: crawler.sessionKey,
                    baseUrl: crawler.extractBaseUrl(window.location.href)
                });
                alert('CrawlerCore 测试完成，请查看控制台');
            } catch (error) {
                console.error('CrawlerCore 测试失败:', error);
                alert('CrawlerCore 测试失败: ' + error.message);
            }
        };

        // URL处理函数（复制自CrawlerCore）
        function extractBaseUrl(urlString) {
            try {
                const url = new URL(urlString);
                
                const paginationParams = [
                    'page', 'p', 'pagenum', 'pageindex', 'pn', 'offset', 'start',
                    'currentpage', 'pageNo', 'pageNumber', 'pg', 'pagesize', 'limit',
                    'from', 'to', 'skip', 'take', 'first', 'last', 'after', 'before'
                ];
                
                paginationParams.forEach(param => {
                    url.searchParams.delete(param);
                });
                
                let pathname = url.pathname;
                pathname = pathname.replace(/\/\d+\/?$/, '/');
                
                const paginationPathPatterns = [
                    /\/page\/\d+/gi,
                    /\/p\/\d+/gi,
                    /\/\d+\.html?$/gi,
                    /\/\d+\.php$/gi,
                    /\/\d+\/$/gi
                ];
                
                paginationPathPatterns.forEach(pattern => {
                    pathname = pathname.replace(pattern, '');
                });
                
                if (!pathname.endsWith('/')) {
                    pathname += '/';
                }
                
                return url.origin + pathname + url.search;
            } catch (error) {
                return urlString.split('?')[0].split('#')[0];
            }
        }

        function hashCode(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString(36);
        }

        function generateSessionKey(url) {
            const baseUrl = extractBaseUrl(url);
            return `session_${hashCode(baseUrl)}`;
        }

        // 更新当前信息
        window.updateCurrentInfo = function() {
            const currentUrl = window.location.href;
            const baseUrl = extractBaseUrl(currentUrl);
            const sessionKey = generateSessionKey(currentUrl);
            
            document.getElementById('currentUrl').textContent = currentUrl;
            document.getElementById('baseUrl').textContent = baseUrl;
            document.getElementById('sessionKey').textContent = sessionKey;
        };

        // 运行测试
        window.runTests = function() {
            const testUrls = [
                'http://example.com/list',
                'http://example.com/list?page=1',
                'http://example.com/list?page=2',
                'http://example.com/list?page=10',
                'http://example.com/list?p=1',
                'http://example.com/list?pagenum=5',
                'http://example.com/list/page/1',
                'http://example.com/list/page/2',
                'http://example.com/list/1',
                'http://example.com/list/2',
                'http://example.com/list/1.html',
                'http://example.com/list/2.html',
                'http://example.com/forum?page=1&category=tech',
                'http://example.com/forum?page=2&category=tech',
            ];

            const results = testUrls.map(url => {
                const baseUrl = extractBaseUrl(url);
                const sessionKey = generateSessionKey(url);
                return { url, baseUrl, sessionKey };
            });

            // 显示结果
            const container = document.getElementById('testResults');
            container.innerHTML = '';

            results.forEach(result => {
                const div = document.createElement('div');
                div.className = 'url-test';
                div.innerHTML = `
                    <strong>原始URL:</strong> ${result.url}<br>
                    <div class="result"><strong>基础URL:</strong> ${result.baseUrl}</div>
                    <div class="result"><strong>Session Key:</strong> ${result.sessionKey}</div>
                `;
                container.appendChild(div);
            });

            // 检查相同基础URL的session key是否一致
            const groupedByBase = {};
            results.forEach(result => {
                if (!groupedByBase[result.baseUrl]) {
                    groupedByBase[result.baseUrl] = [];
                }
                groupedByBase[result.baseUrl].push(result);
            });

            console.log('分组结果:', groupedByBase);
        };

        // 分页导航
        window.goToPage = function(page) {
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            window.location.href = url.toString();
        };

        window.goToPageWithPath = function(page) {
            const url = new URL(window.location);
            url.pathname = url.pathname.replace(/\/page\/\d+/, '') + `/page/${page}`;
            window.location.href = url.toString();
        };

        // 页面加载时更新信息
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentInfo();
        });
    </script>
</body>
</html>
