/**
 * 数据提取器 - 从页面元素中提取结构化数据
 */
export class DataExtractor {
  constructor(config) {
    this.config = config;
  }

  /**
   * 从列表项中提取数据
   */
  extractFromItems(items) {
    const extractedData = [];
    
    items.forEach((item, index) => {
      try {
        const itemData = this.extractFromSingleItem(item, index);
        if (itemData && Object.keys(itemData).length > 0) {
          extractedData.push(itemData);
        }
      } catch (error) {
        console.error(`提取第${index + 1}项数据失败:`, error);
      }
    });
    
    return extractedData;
  }

  /**
   * 从单个列表项中提取数据
   */
  extractFromSingleItem(item, index) {
    const data = {
      index: index + 1,
      extractedAt: new Date().toISOString(),
      url: window.location.href
    };

    // 提取标题
    data.title = this.extractField(item, 'title');
    
    // 提取链接
    data.link = this.extractField(item, 'link', 'href');
    if (data.link && !data.link.startsWith('http')) {
      data.link = new URL(data.link, window.location.origin).href;
    }
    
    // 提取作者
    data.author = this.extractField(item, 'author');
    
    // 提取时间
    data.time = this.extractField(item, 'time', 'title') || this.extractField(item, 'time');
    
    // 提取回复数
    data.replies = this.extractField(item, 'replies');
    if (data.replies) {
      data.replies = this.parseNumber(data.replies);
    }
    
    // 提取内容
    data.content = this.extractField(item, 'content');
    
    // 提取图片
    data.images = this.extractImages(item);
    
    // 提取所有链接
    data.allLinks = this.extractAllLinks(item);
    
    return data;
  }

  /**
   * 提取指定字段
   */
  extractField(item, fieldName, attribute = 'textContent') {
    const selectors = this.config.extractionRules[fieldName] || [];
    
    for (const selector of selectors) {
      try {
        const element = item.querySelector(selector);
        if (element) {
          let value;
          if (attribute === 'textContent') {
            value = element.textContent?.trim();
          } else if (attribute === 'innerHTML') {
            value = element.innerHTML?.trim();
          } else {
            value = element.getAttribute(attribute)?.trim();
          }
          
          if (value) {
            return value;
          }
        }
      } catch (error) {
        console.warn(`选择器 "${selector}" 执行失败:`, error);
      }
    }
    
    return null;
  }

  /**
   * 提取图片
   */
  extractImages(item) {
    const images = [];
    const imgElements = item.querySelectorAll('img');
    
    imgElements.forEach(img => {
      const src = img.src || img.getAttribute('data-src');
      if (src) {
        images.push({
          src: src.startsWith('http') ? src : new URL(src, window.location.origin).href,
          alt: img.alt || '',
          width: img.width || null,
          height: img.height || null
        });
      }
    });
    
    return images;
  }

  /**
   * 提取所有链接
   */
  extractAllLinks(item) {
    const links = [];
    const linkElements = item.querySelectorAll('a[href]');
    
    linkElements.forEach(link => {
      const href = link.href;
      if (href && href !== '#') {
        links.push({
          href: href.startsWith('http') ? href : new URL(href, window.location.origin).href,
          text: link.textContent?.trim() || '',
          title: link.title || ''
        });
      }
    });
    
    return links;
  }

  /**
   * 解析数字
   */
  parseNumber(str) {
    if (!str) return null;
    
    // 移除非数字字符，保留数字
    const numStr = str.toString().replace(/[^\d]/g, '');
    const num = parseInt(numStr, 10);
    
    return isNaN(num) ? null : num;
  }

  /**
   * 清理文本
   */
  cleanText(text) {
    if (!text) return '';
    
    return text
      .replace(/\s+/g, ' ')  // 合并多个空白字符
      .replace(/[\r\n\t]/g, ' ')  // 替换换行符和制表符
      .trim();
  }

  /**
   * 提取元数据
   */
  extractMetadata(item) {
    const metadata = {};
    
    // 提取所有data-*属性
    Array.from(item.attributes).forEach(attr => {
      if (attr.name.startsWith('data-')) {
        metadata[attr.name] = attr.value;
      }
    });
    
    // 提取class信息
    if (item.className) {
      metadata.classes = item.className.split(' ').filter(cls => cls.trim());
    }
    
    // 提取id
    if (item.id) {
      metadata.id = item.id;
    }
    
    return metadata;
  }

  /**
   * 验证提取的数据
   */
  validateData(data) {
    const errors = [];
    
    // 检查必要字段
    if (!data.title && !data.content) {
      errors.push('缺少标题和内容');
    }
    
    // 检查链接格式
    if (data.link && !this.isValidUrl(data.link)) {
      errors.push('链接格式无效');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 检查URL是否有效
   */
  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  /**
   * 获取提取统计信息
   */
  getExtractionStats(extractedData) {
    const stats = {
      totalItems: extractedData.length,
      withTitle: 0,
      withLink: 0,
      withAuthor: 0,
      withTime: 0,
      withReplies: 0,
      withContent: 0,
      withImages: 0
    };
    
    extractedData.forEach(item => {
      if (item.title) stats.withTitle++;
      if (item.link) stats.withLink++;
      if (item.author) stats.withAuthor++;
      if (item.time) stats.withTime++;
      if (item.replies !== null) stats.withReplies++;
      if (item.content) stats.withContent++;
      if (item.images && item.images.length > 0) stats.withImages++;
    });
    
    return stats;
  }
}
