/**
 * 页面解析器 - 解析页面结构并识别列表元素
 */
export class PageParser {
  constructor(config) {
    this.config = config;
  }

  /**
   * 解析页面并找到列表项
   */
  parsePageItems() {
    const result = {
      items: [],
      selector: null,
      containerInfo: null,
      parseStats: null
    };

    // 尝试不同的选择器找到列表项
    const detectedContainer = this.detectListContainer();
    
    if (detectedContainer) {
      result.items = detectedContainer.items;
      result.selector = detectedContainer.selector;
      result.containerInfo = detectedContainer.containerInfo;
      result.parseStats = this.generateParseStats(detectedContainer.items);
    }

    return result;
  }

  /**
   * 检测列表容器
   */
  detectListContainer() {
    const selectors = this.config.listSelectors;
    const candidates = [];

    // 测试每个选择器
    for (const selector of selectors) {
      try {
        const elements = document.querySelectorAll(selector);
        if (elements.length >= 1) { // 至少1个元素
          const score = this.scoreListCandidate(elements, selector);
          candidates.push({
            selector,
            elements: Array.from(elements),
            score,
            count: elements.length
          });
        }
      } catch (error) {
        console.warn(`选择器 "${selector}" 执行失败:`, error);
      }
    }

    // 按分数排序，选择最佳候选
    candidates.sort((a, b) => b.score - a.score);
    
    if (candidates.length > 0) {
      const best = candidates[0];
      return {
        selector: best.selector,
        items: best.elements,
        containerInfo: this.analyzeContainer(best.elements),
        candidates: candidates.slice(0, 3) // 保留前3个候选
      };
    }

    return null;
  }

  /**
   * 为列表候选打分
   */
  scoreListCandidate(elements, selector) {
    let score = 0;
    
    // 基础分数：元素数量
    score += Math.min(elements.length * 10, 100);
    
    // 检查元素的一致性
    const consistencyScore = this.checkElementConsistency(elements);
    score += consistencyScore * 50;
    
    // 检查是否包含常见的列表内容
    const contentScore = this.checkListContent(elements);
    score += contentScore * 30;
    
    // 选择器优先级加分
    const selectorScore = this.getSelectorPriority(selector);
    score += selectorScore * 20;
    
    // 检查元素的位置分布
    const distributionScore = this.checkElementDistribution(elements);
    score += distributionScore * 10;

    return score;
  }

  /**
   * 检查元素一致性
   */
  checkElementConsistency(elements) {
    if (elements.length < 2) return 0;
    
    let consistencyScore = 0;
    const firstElement = elements[0];
    
    // 检查标签名一致性
    const sameTagName = Array.from(elements).every(el => el.tagName === firstElement.tagName);
    if (sameTagName) consistencyScore += 0.3;
    
    // 检查类名相似性
    const firstClasses = firstElement.className.split(' ').filter(c => c);
    if (firstClasses.length > 0) {
      const classSimilarity = Array.from(elements).reduce((acc, el) => {
        const classes = el.className.split(' ').filter(c => c);
        const commonClasses = firstClasses.filter(c => classes.includes(c));
        return acc + (commonClasses.length / Math.max(firstClasses.length, classes.length));
      }, 0) / elements.length;
      
      consistencyScore += classSimilarity * 0.4;
    }
    
    // 检查结构相似性
    const firstChildCount = firstElement.children.length;
    if (firstChildCount > 0) {
      const structureSimilarity = Array.from(elements).reduce((acc, el) => {
        const similarity = Math.abs(el.children.length - firstChildCount) <= 1 ? 1 : 0;
        return acc + similarity;
      }, 0) / elements.length;
      
      consistencyScore += structureSimilarity * 0.3;
    }
    
    return Math.min(consistencyScore, 1);
  }

  /**
   * 检查列表内容特征
   */
  checkListContent(elements) {
    let contentScore = 0;
    const totalElements = elements.length;
    
    // 检查是否包含链接
    const elementsWithLinks = Array.from(elements).filter(el => el.querySelector('a')).length;
    contentScore += (elementsWithLinks / totalElements) * 0.3;
    
    // 检查是否包含标题类元素
    const elementsWithTitles = Array.from(elements).filter(el => 
      el.querySelector('h1, h2, h3, h4, h5, h6, .title, .item_title')
    ).length;
    contentScore += (elementsWithTitles / totalElements) * 0.2;
    
    // 检查是否包含时间信息
    const elementsWithTime = Array.from(elements).filter(el => {
      const text = el.textContent;
      return /\d{4}[-\/]\d{1,2}[-\/]\d{1,2}|\d{1,2}:\d{2}|前|ago|天|小时|分钟/.test(text);
    }).length;
    contentScore += (elementsWithTime / totalElements) * 0.2;
    
    // 检查是否包含作者信息
    const elementsWithAuthor = Array.from(elements).filter(el =>
      el.querySelector('.author, .user, .username') || 
      /作者|by|@/.test(el.textContent)
    ).length;
    contentScore += (elementsWithAuthor / totalElements) * 0.15;
    
    // 检查文本长度合理性
    const avgTextLength = Array.from(elements).reduce((acc, el) => 
      acc + el.textContent.trim().length, 0) / totalElements;
    if (avgTextLength > 20 && avgTextLength < 500) {
      contentScore += 0.15;
    }
    
    return Math.min(contentScore, 1);
  }

  /**
   * 获取选择器优先级
   */
  getSelectorPriority(selector) {
    const priorities = {
      '.cell.item': 1.0,      // V2EX特定
      '.item': 0.8,
      '.list-item': 0.8,
      '.post': 0.7,
      '.article': 0.7,
      'li': 0.6,
      'tr': 0.4
    };
    
    return priorities[selector] || 0.3;
  }

  /**
   * 检查元素分布
   */
  checkElementDistribution(elements) {
    if (elements.length < 2) return 0;
    
    // 检查元素是否在页面上均匀分布
    const positions = Array.from(elements).map(el => {
      const rect = el.getBoundingClientRect();
      return rect.top + window.scrollY;
    });
    
    // 计算位置间距的标准差
    const avgPosition = positions.reduce((a, b) => a + b, 0) / positions.length;
    const variance = positions.reduce((acc, pos) => acc + Math.pow(pos - avgPosition, 2), 0) / positions.length;
    const stdDev = Math.sqrt(variance);
    
    // 标准差越小，分布越均匀，得分越高
    const maxStdDev = 1000; // 假设的最大标准差
    return Math.max(0, 1 - (stdDev / maxStdDev));
  }

  /**
   * 分析容器信息
   */
  analyzeContainer(elements) {
    if (elements.length === 0) return null;
    
    const firstElement = elements[0];
    const lastElement = elements[elements.length - 1];
    
    // 找到共同的父容器
    let commonParent = firstElement.parentElement;
    while (commonParent && !commonParent.contains(lastElement)) {
      commonParent = commonParent.parentElement;
    }
    
    return {
      count: elements.length,
      commonParent: commonParent,
      parentTagName: commonParent?.tagName,
      parentClasses: commonParent?.className.split(' ').filter(c => c),
      firstElementRect: firstElement.getBoundingClientRect(),
      lastElementRect: lastElement.getBoundingClientRect(),
      avgHeight: this.calculateAverageHeight(elements),
      hasImages: elements.some(el => el.querySelector('img')),
      hasLinks: elements.some(el => el.querySelector('a')),
      avgTextLength: this.calculateAverageTextLength(elements)
    };
  }

  /**
   * 计算平均高度
   */
  calculateAverageHeight(elements) {
    const heights = Array.from(elements).map(el => el.getBoundingClientRect().height);
    return heights.reduce((a, b) => a + b, 0) / heights.length;
  }

  /**
   * 计算平均文本长度
   */
  calculateAverageTextLength(elements) {
    const lengths = Array.from(elements).map(el => el.textContent.trim().length);
    return lengths.reduce((a, b) => a + b, 0) / lengths.length;
  }

  /**
   * 生成解析统计信息
   */
  generateParseStats(items) {
    return {
      totalItems: items.length,
      avgTextLength: this.calculateAverageTextLength(items),
      avgHeight: this.calculateAverageHeight(items),
      itemsWithLinks: items.filter(item => item.querySelector('a')).length,
      itemsWithImages: items.filter(item => item.querySelector('img')).length,
      parsedAt: new Date().toISOString()
    };
  }

  /**
   * 验证解析结果
   */
  validateParseResult(result) {
    const issues = [];
    
    if (!result.items || result.items.length === 0) {
      issues.push('未找到任何列表项');
    }
    
    if (result.items && result.items.length < 2) {
      issues.push('列表项数量过少，可能不是真正的列表');
    }
    
    if (result.parseStats && result.parseStats.avgTextLength < 10) {
      issues.push('列表项文本内容过少');
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      confidence: this.calculateConfidence(result)
    };
  }

  /**
   * 计算解析置信度
   */
  calculateConfidence(result) {
    if (!result.items || result.items.length === 0) return 0;
    
    let confidence = 0.5; // 基础置信度
    
    // 根据项目数量调整
    if (result.items.length >= 5) confidence += 0.2;
    if (result.items.length >= 10) confidence += 0.1;
    
    // 根据内容质量调整
    if (result.parseStats) {
      if (result.parseStats.itemsWithLinks > result.items.length * 0.5) confidence += 0.1;
      if (result.parseStats.avgTextLength > 30) confidence += 0.1;
    }
    
    return Math.min(confidence, 1);
  }
}
