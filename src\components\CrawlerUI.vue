<template>
  <div class="crawler-ui">
    <!-- 主控制面板 -->
    <el-card class="control-panel" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>网页爬虫控制台</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="autoDetect"
            :loading="detecting"
          >
            自动检测
          </el-button>
        </div>
      </template>

      <!-- 状态显示 -->
      <div class="status-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="当前页面" :value="currentPage" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已爬取项目" :value="totalItems" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="检测到列表项" :value="detectedItems" />
          </el-col>
          <el-col :span="6">
            <div class="status-indicator">
              <span>状态: </span>
              <el-tag 
                :type="statusType" 
                :effect="isRunning ? 'dark' : 'plain'"
              >
                {{ statusText }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 控制按钮 -->
      <div class="control-buttons">
        <el-button-group>
          <el-button 
            type="primary" 
            @click="startCrawling"
            :disabled="isRunning"
            :loading="isRunning && !isPaused"
          >
            开始爬取
          </el-button>
          
          <el-button 
            @click="pauseResume"
            :disabled="!isRunning"
            :type="isPaused ? 'success' : 'warning'"
          >
            {{ isPaused ? '恢复' : '暂停' }}
          </el-button>
          
          <el-button 
            type="danger" 
            @click="stopCrawling"
            :disabled="!isRunning"
          >
            停止
          </el-button>
        </el-button-group>

        <el-button 
          @click="previewPage"
          :loading="previewing"
        >
          预览当前页
        </el-button>

        <el-button 
          @click="showSettings = true"
        >
          设置
        </el-button>
      </div>

      <!-- 进度条 -->
      <div v-if="isRunning" class="progress-section">
        <el-progress 
          :percentage="progressPercentage" 
          :status="progressStatus"
          :stroke-width="8"
        >
          <template #default="{ percentage }">
            <span class="progress-text">{{ progressText }}</span>
          </template>
        </el-progress>
      </div>
    </el-card>

    <!-- 检测结果 -->
    <el-card v-if="detectionResult" class="detection-result" shadow="hover">
      <template #header>
        <span>页面检测结果</span>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="检测到的列表选择器">
          <el-tag>{{ detectionResult.detectedListSelector }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="列表项数量">
          {{ detectionResult.detectedItemCount }}
        </el-descriptions-item>
        <el-descriptions-item label="分页检测" :span="2">
          <div v-if="detectionResult.detectedPagination">
            <el-tag v-if="detectionResult.detectedPagination.nextButton" type="success">
              找到下一页按钮
            </el-tag>
            <el-tag v-if="detectionResult.detectedPagination.pageLinks" type="info">
              找到 {{ detectionResult.detectedPagination.pageLinks.count }} 个页码链接
            </el-tag>
          </div>
          <el-tag v-else type="warning">未检测到分页</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 数据预览 -->
    <el-card v-if="previewData.length > 0" class="preview-data" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>数据预览 ({{ previewData.length }} 项)</span>
          <el-button 
            type="success" 
            size="small" 
            @click="exportData"
          >
            导出数据
          </el-button>
        </div>
      </template>

      <el-table 
        :data="previewData.slice(0, 10)" 
        style="width: 100%"
        max-height="400"
      >
        <el-table-column prop="index" label="#" width="60" />
        <el-table-column prop="title" label="标题" min-width="200">
          <template #default="{ row }">
            <a v-if="row.link" :href="row.link" target="_blank" class="title-link">
              {{ row.title || '无标题' }}
            </a>
            <span v-else>{{ row.title || '无标题' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="time" label="时间" width="150" />
        <el-table-column prop="replies" label="回复" width="80" />
      </el-table>

      <div v-if="previewData.length > 10" class="more-data-hint">
        还有 {{ previewData.length - 10 }} 项数据...
      </div>
    </el-card>

    <!-- 日志面板 -->
    <el-card class="log-panel" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>运行日志</span>
          <el-button 
            size="small" 
            @click="clearLogs"
          >
            清空日志
          </el-button>
        </div>
      </template>

      <div class="log-container">
        <div 
          v-for="(log, index) in logs" 
          :key="index"
          :class="['log-item', `log-${log.type}`]"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>

    <!-- 设置对话框 -->
    <el-dialog 
      v-model="showSettings" 
      title="爬虫设置" 
      width="600px"
    >
      <el-form :model="settings" label-width="120px">
        <el-form-item label="最大页数">
          <el-input-number 
            v-model="settings.maxPages" 
            :min="1" 
            :max="100" 
          />
        </el-form-item>
        <el-form-item label="页面延迟(ms)">
          <el-input-number 
            v-model="settings.delay" 
            :min="500" 
            :max="10000" 
            :step="500"
          />
        </el-form-item>
        <el-form-item label="重试次数">
          <el-input-number 
            v-model="settings.retryCount" 
            :min="0" 
            :max="10" 
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CrawlerCore } from '../crawler/CrawlerCore.js'
import { storage } from '../utils/storage.js'

// 响应式数据
const crawler = ref(null)
const detecting = ref(false)
const previewing = ref(false)
const showSettings = ref(false)

const currentPage = ref(1)
const totalItems = ref(0)
const detectedItems = ref(0)
const isRunning = ref(false)
const isPaused = ref(false)

const detectionResult = ref(null)
const previewData = ref([])
const logs = ref([])

const settings = reactive({
  maxPages: 10,
  delay: 2000,
  retryCount: 3
})

// 计算属性
const statusText = computed(() => {
  if (isRunning.value) {
    return isPaused.value ? '已暂停' : '运行中'
  }
  return '就绪'
})

const statusType = computed(() => {
  if (isRunning.value) {
    return isPaused.value ? 'warning' : 'success'
  }
  return 'info'
})

const progressPercentage = computed(() => {
  if (!isRunning.value || settings.maxPages === 0) return 0
  return Math.round((currentPage.value / settings.maxPages) * 100)
})

const progressStatus = computed(() => {
  if (isPaused.value) return 'warning'
  if (isRunning.value) return 'success'
  return 'info'
})

const progressText = computed(() => {
  return `第 ${currentPage.value} 页 / 最多 ${settings.maxPages} 页`
})

// 方法
const initCrawler = () => {
  crawler.value = new CrawlerCore()
  
  // 绑定事件监听器
  crawler.value.on('crawlStart', onCrawlStart)
  crawler.value.on('crawlComplete', onCrawlComplete)
  crawler.value.on('crawlError', onCrawlError)
  crawler.value.on('pageStart', onPageStart)
  crawler.value.on('pageComplete', onPageComplete)
  crawler.value.on('pageError', onPageError)
  crawler.value.on('configDetected', onConfigDetected)
}

const autoDetect = async () => {
  detecting.value = true
  try {
    const result = crawler.value.autoDetectConfig()
    detectionResult.value = result
    
    if (result.detectedListSelector) {
      detectedItems.value = result.detectedItemCount || 0
      addLog('success', `检测到 ${detectedItems.value} 个列表项`)
    } else {
      addLog('warning', '未检测到列表结构')
    }
  } catch (error) {
    addLog('error', `自动检测失败: ${error.message}`)
  } finally {
    detecting.value = false
  }
}

const previewPage = async () => {
  previewing.value = true
  try {
    const result = await crawler.value.previewCurrentPage()
    
    if (result.success) {
      previewData.value = result.data.items
      addLog('success', `预览成功，获取到 ${result.data.items.length} 项数据`)
    } else {
      addLog('error', `预览失败: ${result.error}`)
    }
  } catch (error) {
    addLog('error', `预览失败: ${error.message}`)
  } finally {
    previewing.value = false
  }
}

const startCrawling = async () => {
  try {
    await crawler.value.startCrawling(settings)
  } catch (error) {
    ElMessage.error(`爬取失败: ${error.message}`)
  }
}

const pauseResume = () => {
  if (isPaused.value) {
    crawler.value.resume()
  } else {
    crawler.value.pause()
  }
}

const stopCrawling = () => {
  crawler.value.stop()
}

const exportData = () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }
  
  const filename = `crawler_data_${new Date().toISOString().slice(0, 10)}`
  storage.exportToFile(previewData.value, filename, 'json')
  addLog('success', '数据已导出')
}

const saveSettings = () => {
  crawler.value.updateConfig({
    crawlSettings: { ...settings }
  })
  showSettings.value = false
  addLog('info', '设置已保存')
}

const clearLogs = () => {
  logs.value = []
}

const addLog = (type, message) => {
  logs.value.unshift({
    type,
    message,
    timestamp: new Date()
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

const formatTime = (timestamp) => {
  return timestamp.toLocaleTimeString()
}

// 事件处理器
const onCrawlStart = () => {
  isRunning.value = true
  isPaused.value = false
  addLog('info', '开始爬取')
}

const onCrawlComplete = (data) => {
  isRunning.value = false
  isPaused.value = false
  previewData.value = data.data
  addLog('success', `爬取完成，共获取 ${data.data.length} 项数据`)
  ElMessage.success('爬取完成！')
}

const onCrawlError = (data) => {
  isRunning.value = false
  isPaused.value = false
  addLog('error', `爬取出错: ${data.error.message}`)
  ElMessage.error('爬取出错')
}

const onPageStart = (data) => {
  currentPage.value = data.pageNumber
  addLog('info', `开始爬取第 ${data.pageNumber} 页`)
}

const onPageComplete = (data) => {
  totalItems.value = data.totalItems
  addLog('success', `第 ${data.pageNumber} 页完成，获取 ${data.itemCount} 项`)
}

const onPageError = (data) => {
  addLog('error', `第 ${data.pageNumber} 页出错: ${data.error}`)
}

const onConfigDetected = (data) => {
  detectionResult.value = data
}

// 生命周期
onMounted(() => {
  initCrawler()
  addLog('info', '爬虫已初始化')
})

onUnmounted(() => {
  if (crawler.value) {
    crawler.value.stop()
  }
})
</script>

<style scoped>
.crawler-ui {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-panel {
  margin-bottom: 20px;
}

.status-section {
  margin-bottom: 20px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.progress-section {
  margin-top: 20px;
}

.progress-text {
  font-size: 12px;
  color: #666;
}

.detection-result {
  margin-bottom: 20px;
}

.preview-data {
  margin-bottom: 20px;
}

.title-link {
  color: #409eff;
  text-decoration: none;
}

.title-link:hover {
  text-decoration: underline;
}

.more-data-hint {
  text-align: center;
  color: #999;
  margin-top: 10px;
  font-size: 14px;
}

.log-panel {
  margin-bottom: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.log-time {
  color: #666;
  font-family: monospace;
  white-space: nowrap;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-info {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.log-success {
  background: #e8f5e8;
  border-left: 3px solid #4caf50;
}

.log-warning {
  background: #fff3e0;
  border-left: 3px solid #ff9800;
}

.log-error {
  background: #ffebee;
  border-left: 3px solid #f44336;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .crawler-ui {
    padding: 10px;
  }

  .control-buttons {
    flex-direction: column;
  }

  .control-buttons .el-button-group {
    width: 100%;
  }

  .control-buttons .el-button-group .el-button {
    flex: 1;
  }
}

/* 滚动条样式 */
.log-container::-webkit-scrollbar {
  width: 6px;
}

.log-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.log-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 状态指示器动画 */
.status-indicator .el-tag {
  transition: all 0.3s ease;
}

/* 进度条自定义样式 */
.progress-section :deep(.el-progress-bar__outer) {
  background-color: #f0f2f5;
}

.progress-section :deep(.el-progress-bar__inner) {
  transition: width 0.6s ease;
}

/* 表格样式优化 */
.preview-data :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

.preview-data :deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

.preview-data :deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

.preview-data :deep(.el-table tr:hover td) {
  background-color: #f8f9fa;
}
</style>
